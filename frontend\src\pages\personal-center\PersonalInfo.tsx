import {
  ClockCircleOutlined,
  SettingOutlined,
  TeamOutlined,
  UserOutlined,
} from '@ant-design/icons';
import {
  <PERSON>ert,
  Avatar,
  Button,
  Space,
  Spin,
  Typography,
} from 'antd';
import { ProCard } from '@ant-design/pro-components';

import React, { useEffect, useState } from 'react';
import { UserService } from '@/services/user';
import type { UserProfileDetailResponse } from '@/types/api';
import UnifiedSettingsModal from './UnifiedSettingsModal';
import UserInfoPopover from './UserInfoPopover';
import styles from './PersonalInfo.module.css';

const { Title, Text } = Typography;

/**
 * 个人信息组件
 *
 * 显示用户的基本个人信息，采用简洁的卡片设计。
 * 用户名支持悬浮显示详细信息（电话、邮箱、注册时间等）。
 *
 * 主要功能：
 * 1. 显示用户头像（使用姓名首字母）
 * 2. 显示用户姓名（支持气泡卡片显示详细信息）
 * 3. 显示最后登录时间和登录团队
 * 4. 提供设置入口
 *
 * 数据来源：
 * - 用户详细信息：通过UserService.getUserProfileDetail()获取
 */
const PersonalInfo: React.FC = () => {
  /**
   * 用户详细信息状态管理
   */
  const [userInfo, setUserInfo] = useState<UserProfileDetailResponse>({
    name: '',
    position: '',
    email: '',
    telephone: '',
    registerDate: '',
    lastLoginTime: '',
    lastLoginTeam: '',
    teamCount: 0,
    avatar: '',
  });

  const [userInfoLoading, setUserInfoLoading] = useState(true);
  const [userInfoError, setUserInfoError] = useState<string | null>(null);

  // Modal状态管理
  const [settingsModalVisible, setSettingsModalVisible] = useState(false);

  // 获取用户数据
  useEffect(() => {
    const fetchUserData = async () => {
      try {
        const userDetail = await UserService.getUserProfileDetail();
        setUserInfo(userDetail);
        setUserInfoError(null);
      } catch (error) {
        console.error('获取用户详细信息失败:', error);
        setUserInfoError('获取用户详细信息失败，请稍后重试');
      } finally {
        setUserInfoLoading(false);
      }
    };

    fetchUserData();
  }, []);

  return (
    <ProCard
      title="个人信息"
      extra={
        <Button
          type="text"
          icon={<SettingOutlined />}
          onClick={() => setSettingsModalVisible(true)}
        />
      }
      style={{
        marginBottom: 16,
        borderRadius: 12,
        boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',
      }}
    >
      {userInfoError ? (
        <Alert
          message="个人信息加载失败"
          description={userInfoError}
          type="error"
          showIcon
          style={{
            borderRadius: 12,
            border: 'none',
          }}
        />
      ) : (
        <Spin spinning={userInfoLoading}>
          {/* 主要内容区域 */}
          <div className={styles.personalInfoContent}>
            <div className={styles.userInfoSection}>
              {/* 头像区域 */}
              <div className={styles.avatarContainer}>
                <Avatar
                  size={80}
                  className={styles.avatar}
                  icon={!userInfo.name && <UserOutlined />}
                >
                  {userInfo.name?.charAt(0).toUpperCase()}
                </Avatar>
                {/* 在线状态指示器 */}
                <div className={styles.onlineIndicator} />
              </div>

              {/* 基本信息 */}
              <div className={styles.userBasicInfo}>
                <UserInfoPopover userInfo={userInfo}>
                  <Typography.Title
                    level={3}
                    className={styles.userName}
                  >
                    {userInfo.name || '加载中...'}
                  </Typography.Title>
                </UserInfoPopover>

                {/* 职位信息（如果有） */}
                {userInfo.position && (
                  <Text
                    type="secondary"
                    style={{
                      fontSize: 14,
                      marginBottom: 12,
                      display: 'block',
                    }}
                  >
                    {userInfo.position}
                  </Text>
                )}
              </div>
            </div>

            {/* 登录信息区域 */}
            <div className={styles.loginInfoSection}>
              <Space direction="vertical" size={8} style={{ width: '100%' }}>
                {userInfo.lastLoginTime && (
                  <div className={styles.loginInfoItem}>
                    <ClockCircleOutlined
                      style={{
                        fontSize: 14,
                        color: '#1890ff',
                      }}
                    />
                    <Typography.Text
                      style={{
                        fontSize: 13,
                        color: '#8c8c8c',
                        fontWeight: 500,
                      }}
                    >
                      最后登录：{userInfo.lastLoginTime}
                    </Typography.Text>
                  </div>
                )}
                {userInfo.lastLoginTeam && (
                  <div className={styles.loginInfoItem}>
                    <TeamOutlined
                      style={{
                        fontSize: 14,
                        color: '#52c41a',
                      }}
                    />
                    <Typography.Text
                      style={{
                        fontSize: 13,
                        color: '#8c8c8c',
                        fontWeight: 500,
                      }}
                    >
                      团队：{userInfo.lastLoginTeam}
                    </Typography.Text>
                  </div>
                )}
              </Space>
            </div>
          </div>
        </Spin>
      )}

      {/* 统一设置Modal */}
      <UnifiedSettingsModal
        visible={settingsModalVisible}
        onCancel={() => setSettingsModalVisible(false)}
        userInfo={userInfo}
        onSuccess={() => {
          // 可以在这里刷新用户信息
          console.log('设置操作成功');
        }}
      />
    </ProCard>
  );
};

export default PersonalInfo;
