import {
  ClockCircleOutlined,
  MailOutlined,
  PhoneOutlined,
  SettingOutlined,
  TeamOutlined,
  UserOutlined,
  CarOutlined,
  UsergroupAddOutlined,
  ExclamationCircleOutlined,
  AlertOutlined,
} from '@ant-design/icons';
import {
  Alert,
  Avatar,
  Button,
  Card,
  Col,
  Row,
  Space,
  Spin,
  Typography,
} from 'antd';
import { ProCard } from '@ant-design/pro-components';

import React, { useEffect, useState } from 'react';
import { UserService } from '@/services/user';
import type { UserProfileDetailResponse, UserPersonalStatsResponse } from '@/types/api';
import UnifiedSettingsModal from './UnifiedSettingsModal';
import styles from './PersonalInfo.module.css';

const { Title, Text } = Typography;

/**
 * 个人信息组件
 *
 * 显示用户的完整个人信息，包括基本信息和登录历史。
 * 整合了原UserProfileCard和LastLoginInfo组件的功能。
 *
 * 主要功能：
 * 1. 显示用户头像（使用姓名首字母）
 * 2. 显示用户姓名、邮箱、电话
 * 3. 显示注册日期
 * 4. 显示最后登录时间和登录团队
 *
 * 数据来源：
 * - 用户详细信息：通过UserService.getUserProfileDetail()获取
 */
const PersonalInfo: React.FC = () => {
  /**
   * 用户详细信息状态管理
   */
  const [userInfo, setUserInfo] = useState<UserProfileDetailResponse>({
    name: '',
    position: '',
    email: '',
    telephone: '',
    registerDate: '',
    lastLoginTime: '',
    lastLoginTeam: '',
    teamCount: 0,
    avatar: '',
  });

  const [userInfoLoading, setUserInfoLoading] = useState(true);
  const [userInfoError, setUserInfoError] = useState<string | null>(null);

  // 数据概览状态管理
  const [personalStats, setPersonalStats] = useState<UserPersonalStatsResponse>({
    vehicles: 0,
    personnel: 0,
    warnings: 0,
    alerts: 0,
  });
  const [statsLoading, setStatsLoading] = useState(true);
  const [statsError, setStatsError] = useState<string | null>(null);

  // Modal状态管理
  const [settingsModalVisible, setSettingsModalVisible] = useState(false);

  // 获取用户数据和统计数据
  useEffect(() => {
    const fetchUserData = async () => {
      try {
        const userDetail = await UserService.getUserProfileDetail();
        setUserInfo(userDetail);
        setUserInfoError(null);
      } catch (error) {
        console.error('获取用户详细信息失败:', error);
        setUserInfoError('获取用户详细信息失败，请稍后重试');
      } finally {
        setUserInfoLoading(false);
      }
    };

    const fetchStatsData = async () => {
      try {
        const stats = await UserService.getUserPersonalStats();
        setPersonalStats(stats);
        setStatsError(null);
      } catch (error) {
        console.error('获取统计数据失败:', error);
        setStatsError('获取统计数据失败，请稍后重试');
      } finally {
        setStatsLoading(false);
      }
    };

    fetchUserData();
    fetchStatsData();
  }, []);

  return (
    <ProCard
      title="个人信息"
 
      extra={
        <Button
          type="text"
          icon={<SettingOutlined />}
          onClick={() => setSettingsModalVisible(true)}
        />
      }
    >
        {userInfoError ? (
          <Alert
            message="个人信息加载失败"
            description={userInfoError}
            type="error"
            showIcon
            style={{
              borderRadius: 12,
              border: 'none',
            }}
          />
        ) : (
          <Spin spinning={userInfoLoading || statsLoading}>
            {/* 主要内容区域 */}
            <Row gutter={[24, 16]} align="top">
              {/* 左侧：个人信息 */}
              <Col xs={24} sm={24} md={14} lg={14} xl={14}>
                <div
                  style={{
                    display: 'flex',
                    alignItems: 'flex-start',
                    gap: 20,
                    marginBottom: 12,
                  }}
                >
                  {/* 头像区域 */}
                  <div className={styles.avatarContainer}>
                    <Avatar
                      size={80}
                      className={styles.avatar}
                      icon={!userInfo.name && <UserOutlined />}
                    >
                      {userInfo.name?.charAt(0).toUpperCase()}
                    </Avatar>
                    {/* 在线状态指示器 */}
                    <div className={styles.onlineIndicator} />
                  </div>

                  {/* 基本信息 */}
                  <div style={{ flex: 1, minWidth: 0 }}>
                    <Typography.Title
                      level={3}
                      style={{
                        margin: '0 0 8px 0',
                        fontSize: 24,
                        fontWeight: 600,
                        color: '#262626',
                        lineHeight: 1.2,
                      }}
                    >
                      {userInfo.name || '加载中...'}
                    </Typography.Title>

                    {/* 联系信息 */}
                    <Space direction="vertical" size={8}>
                      {userInfo.email && (
                        <div className={`${styles.contactCard} ${styles.emailCard}`}>
                          <MailOutlined
                            style={{
                              fontSize: 16,
                              color: '#1890ff',
                            }}
                          />
                          <Typography.Text
                            style={{
                              color: '#595959',
                              fontSize: 14,
                              fontWeight: 500,
                            }}
                          >
                            {userInfo.email}
                          </Typography.Text>
                        </div>
                      )}
                      {userInfo.telephone && (
                        <div className={`${styles.contactCard} ${styles.phoneCard}`}>
                          <PhoneOutlined
                            style={{
                              fontSize: 16,
                              color: '#52c41a',
                            }}
                          />
                          <Typography.Text
                            style={{
                              color: '#595959',
                              fontSize: 14,
                              fontWeight: 500,
                            }}
                          >
                            {userInfo.telephone}
                          </Typography.Text>
                        </div>
                      )}
                    </Space>
                  </div>
                </div>

                {/* 附加信息区域 */}
                <div className={styles.additionalInfo}>
                  <Space direction="vertical" size={6} style={{ width: '100%' }}>
                    {userInfo.registerDate && (
                      <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                        <Typography.Text
                          style={{
                            fontSize: 13,
                            color: '#8c8c8c',
                            fontWeight: 500,
                          }}
                        >
                          📅 注册于 {userInfo.registerDate}
                        </Typography.Text>
                      </div>
                    )}
                    {userInfo.lastLoginTime && (
                      <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                        <ClockCircleOutlined
                          style={{
                            fontSize: 13,
                            color: '#1890ff',
                          }}
                        />
                        <Typography.Text
                          style={{
                            fontSize: 13,
                            color: '#8c8c8c',
                            fontWeight: 500,
                          }}
                        >
                          最后登录：{userInfo.lastLoginTime}
                        </Typography.Text>
                      </div>
                    )}
                    {userInfo.lastLoginTeam && (
                      <div style={{ display: 'flex', alignItems: 'center', gap: 8 }}>
                        <TeamOutlined
                          style={{
                            fontSize: 13,
                            color: '#52c41a',
                          }}
                        />
                        <Typography.Text
                          style={{
                            fontSize: 13,
                            color: '#8c8c8c',
                            fontWeight: 500,
                          }}
                        >
                          团队：{userInfo.lastLoginTeam}
                        </Typography.Text>
                      </div>
                    )}
                  </Space>
                </div>
              </Col>

              {/* 右侧：数据概览 */}
              <Col xs={24} sm={24} md={10} lg={10} xl={10}>
                {statsError ? (
                  <Alert
                    message="数据概览加载失败"
                    description={statsError}
                    type="error"
                    showIcon
                    style={{
                      borderRadius: 12,
                      border: 'none',
                    }}
                  />
                ) : (
                  <div>
                    <Typography.Title level={5} className={styles.statsTitle}>
                      数据概览
                    </Typography.Title>
                    <Row gutter={[8, 8]}>
                      {/* 车辆统计 */}
                      <Col xs={12} sm={12} md={12} lg={12} xl={12}>
                        <Card
                          size="small"
                          className={`${styles.statsCard} ${styles.vehicleCard} ${styles.fadeInDelay1}`}
                          styles={{
                            body: {
                              padding: '16px 12px',
                              textAlign: 'center',
                            },
                          }}
                        >
                          <div style={{ marginBottom: 8 }}>
                            <CarOutlined
                              style={{
                                fontSize: 20,
                                color: '#1890ff',
                                marginBottom: 4,
                              }}
                            />
                          </div>
                          <div
                            style={{
                              fontSize: 24,
                              fontWeight: 700,
                              color: '#1890ff',
                              lineHeight: 1,
                              marginBottom: 4,
                            }}
                          >
                            {personalStats.vehicles}
                          </div>
                          <div
                            style={{
                              fontSize: 12,
                              color: '#1890ff',
                              fontWeight: 600,
                              opacity: 0.8,
                            }}
                          >
                            车辆
                          </div>
                        </Card>
                      </Col>

                      {/* 人员统计 */}
                      <Col xs={12} sm={12} md={12} lg={12} xl={12}>
                        <Card
                          size="small"
                          className={`${styles.statsCard} ${styles.personnelCard} ${styles.fadeInDelay2}`}
                          styles={{
                            body: {
                              padding: '16px 12px',
                              textAlign: 'center',
                            },
                          }}
                        >
                          <div style={{ marginBottom: 8 }}>
                            <UsergroupAddOutlined
                              style={{
                                fontSize: 20,
                                color: '#52c41a',
                                marginBottom: 4,
                              }}
                            />
                          </div>
                          <div
                            style={{
                              fontSize: 24,
                              fontWeight: 700,
                              color: '#52c41a',
                              lineHeight: 1,
                              marginBottom: 4,
                            }}
                          >
                            {personalStats.personnel}
                          </div>
                          <div
                            style={{
                              fontSize: 12,
                              color: '#52c41a',
                              fontWeight: 600,
                              opacity: 0.8,
                            }}
                          >
                            人员
                          </div>
                        </Card>
                      </Col>

                      {/* 预警统计 */}
                      <Col xs={12} sm={12} md={12} lg={12} xl={12}>
                        <Card
                          size="small"
                          className={`${styles.statsCard} ${styles.warningCard} ${styles.fadeInDelay3}`}
                          styles={{
                            body: {
                              padding: '16px 12px',
                              textAlign: 'center',
                            },
                          }}
                        >
                          <div style={{ marginBottom: 8 }}>
                            <ExclamationCircleOutlined
                              style={{
                                fontSize: 20,
                                color: '#faad14',
                                marginBottom: 4,
                              }}
                            />
                          </div>
                          <div
                            style={{
                              fontSize: 24,
                              fontWeight: 700,
                              color: '#faad14',
                              lineHeight: 1,
                              marginBottom: 4,
                            }}
                          >
                            {personalStats.warnings}
                          </div>
                          <div
                            style={{
                              fontSize: 12,
                              color: '#faad14',
                              fontWeight: 600,
                              opacity: 0.8,
                            }}
                          >
                            预警
                          </div>
                        </Card>
                      </Col>

                      {/* 告警统计 */}
                      <Col xs={12} sm={12} md={12} lg={12} xl={12}>
                        <Card
                          size="small"
                          className={`${styles.statsCard} ${styles.alertCard} ${styles.fadeInDelay4}`}
                          styles={{
                            body: {
                              padding: '16px 12px',
                              textAlign: 'center',
                            },
                          }}
                        >
                          <div style={{ marginBottom: 8 }}>
                            <AlertOutlined
                              style={{
                                fontSize: 20,
                                color: '#ff4d4f',
                                marginBottom: 4,
                              }}
                            />
                          </div>
                          <div
                            style={{
                              fontSize: 24,
                              fontWeight: 700,
                              color: '#ff4d4f',
                              lineHeight: 1,
                              marginBottom: 4,
                            }}
                          >
                            {personalStats.alerts}
                          </div>
                          <div
                            style={{
                              fontSize: 12,
                              color: '#ff4d4f',
                              fontWeight: 600,
                              opacity: 0.8,
                            }}
                          >
                            告警
                          </div>
                        </Card>
                      </Col>
                    </Row>
                  </div>
                )}
              </Col>
            </Row>
          </Spin>
        )}

      {/* 统一设置Modal */}
      <UnifiedSettingsModal
        visible={settingsModalVisible}
        onCancel={() => setSettingsModalVisible(false)}
        userInfo={userInfo}
        onSuccess={() => {
          // 可以在这里刷新用户信息或团队列表
          console.log('设置操作成功');
        }}
      />
    </ProCard>
  );
};

export default PersonalInfo;
