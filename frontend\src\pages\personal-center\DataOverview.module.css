/* 数据概览卡片样式 */
.statsCard {
  border-radius: 12px;
  border: none;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;
  cursor: pointer;
  height: 120px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.statsCard:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 0.15);
}

.statsCard:active {
  transform: translateY(-2px) scale(0.98);
  transition: all 0.1s ease;
}

/* 统计卡片特定颜色 */
.vehicleCard {
  background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
  border: 1px solid rgba(24, 144, 255, 0.1);
}

.vehicleCard:hover {
  background: linear-gradient(135deg, #bbdefb 0%, #90caf9 100%);
  box-shadow: 0 8px 24px rgba(24, 144, 255, 0.2);
}

.personnelCard {
  background: linear-gradient(135deg, #f6ffed 0%, #d9f7be 100%);
  border: 1px solid rgba(82, 196, 26, 0.1);
}

.personnelCard:hover {
  background: linear-gradient(135deg, #d9f7be 0%, #b7eb8f 100%);
  box-shadow: 0 8px 24px rgba(82, 196, 26, 0.2);
}

.warningCard {
  background: linear-gradient(135deg, #fffbe6 0%, #fff1b8 100%);
  border: 1px solid rgba(250, 173, 20, 0.1);
}

.warningCard:hover {
  background: linear-gradient(135deg, #fff1b8 0%, #ffe58f 100%);
  box-shadow: 0 8px 24px rgba(250, 173, 20, 0.2);
}

.alertCard {
  background: linear-gradient(135deg, #fff2f0 0%, #ffccc7 100%);
  border: 1px solid rgba(255, 77, 79, 0.1);
}

.alertCard:hover {
  background: linear-gradient(135deg, #ffccc7 0%, #ffa39e 100%);
  box-shadow: 0 8px 24px rgba(255, 77, 79, 0.2);
}

/* 渐入动画 */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 渐入动画延迟 */
.fadeInDelay1 {
  animation: fadeInUp 0.6s ease-out 0.1s both;
}

.fadeInDelay2 {
  animation: fadeInUp 0.6s ease-out 0.2s both;
}

.fadeInDelay3 {
  animation: fadeInUp 0.6s ease-out 0.3s both;
}

.fadeInDelay4 {
  animation: fadeInUp 0.6s ease-out 0.4s both;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .statsCard {
    height: 100px;
  }
}

@media (max-width: 576px) {
  .statsCard {
    height: 90px;
  }
}

/* 加载状态 */
.loadingCard {
  background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
  border-radius: 12px;
  height: 120px;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* 成功状态动画 */
.successAnimation {
  animation: successPulse 0.6s ease-out;
}

@keyframes successPulse {
  0% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(82, 196, 26, 0.7);
  }
  50% {
    transform: scale(1.05);
    box-shadow: 0 0 0 10px rgba(82, 196, 26, 0);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 0 0 0 rgba(82, 196, 26, 0);
  }
}

/* 错误状态动画 */
.errorAnimation {
  animation: errorShake 0.6s ease-out;
}

@keyframes errorShake {
  0%, 100% {
    transform: translateX(0);
  }
  10%, 30%, 50%, 70%, 90% {
    transform: translateX(-3px);
  }
  20%, 40%, 60%, 80% {
    transform: translateX(3px);
  }
}
