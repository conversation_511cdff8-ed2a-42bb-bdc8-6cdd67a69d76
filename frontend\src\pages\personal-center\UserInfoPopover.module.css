/* 气泡卡片样式 */
.popoverOverlay {
  z-index: 1050;
}

.popoverContent {
  padding: 4px 0;
  min-width: 280px;
}

.popoverTitle {
  padding: 4px 0;
  border-bottom: 1px solid #f0f0f0;
  margin-bottom: 8px;
}

/* 信息项样式 */
.infoItem {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 8px 0;
  transition: all 0.2s ease;
}

.infoItem:hover {
  background: rgba(0, 0, 0, 0.02);
  border-radius: 6px;
  padding: 8px 8px;
  margin: 0 -8px;
}

.iconWrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border-radius: 50%;
  background: rgba(0, 0, 0, 0.04);
  flex-shrink: 0;
  margin-top: 2px;
}

.icon {
  font-size: 12px;
}

.infoContent {
  flex: 1;
  min-width: 0;
}

.label {
  display: block;
  font-size: 12px;
  line-height: 1.2;
  margin-bottom: 2px;
  opacity: 0.7;
}

.value {
  display: block;
  font-size: 13px;
  font-weight: 500;
  line-height: 1.3;
  word-break: break-all;
}

/* 触发器样式 */
.trigger {
  cursor: pointer;
  transition: all 0.2s ease;
  border-radius: 4px;
  padding: 2px 4px;
  margin: -2px -4px;
}

.trigger:hover {
  background: rgba(24, 144, 255, 0.06);
  color: #1890ff;
}

/* 复制按钮样式优化 */
.value :global(.ant-typography-copy) {
  margin-left: 6px;
  opacity: 0.6;
  transition: opacity 0.2s ease;
}

.value:hover :global(.ant-typography-copy) {
  opacity: 1;
}

/* 分割线样式 */
.popoverContent :global(.ant-divider) {
  background: #f0f0f0;
  margin: 8px 0;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .popoverContent {
    min-width: 260px;
  }
  
  .infoItem {
    gap: 10px;
  }
  
  .iconWrapper {
    width: 20px;
    height: 20px;
  }
  
  .icon {
    font-size: 11px;
  }
  
  .label {
    font-size: 11px;
  }
  
  .value {
    font-size: 12px;
  }
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(-4px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.popoverContent {
  animation: fadeIn 0.2s ease-out;
}

/* 特殊状态样式 */
.infoItem:first-child {
  padding-top: 4px;
}

.infoItem:last-child {
  padding-bottom: 4px;
}

/* 图标颜色变化动画 */
.iconWrapper {
  transition: all 0.2s ease;
}

.infoItem:hover .iconWrapper {
  transform: scale(1.1);
}

/* 文字选择样式 */
.value {
  user-select: text;
}

.label {
  user-select: none;
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  .infoItem:hover {
    background: rgba(255, 255, 255, 0.04);
  }
  
  .iconWrapper {
    background: rgba(255, 255, 255, 0.08);
  }
  
  .trigger:hover {
    background: rgba(24, 144, 255, 0.15);
  }
}
